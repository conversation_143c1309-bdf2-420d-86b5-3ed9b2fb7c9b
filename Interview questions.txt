web api : 

What are Media type formatters in Web API?
 How do we make sure that Web API returns data in JSON format only?
How to provide Alias name for an action method in Web API?
What are HTTP headers, and which ones are most commonly used?
How can you test APIs?


What is idempotency in a RESTful context?
An HTTP method is considered idempotent if it will result in the same outcome no matter how many times it is executed. All read-only methods are idempotent, as are PUT and DELETE. However, POST and PATCH are not idempotent. POST is not idempotent because calling it multiple times will result in multiple resources being created. PATCH can be idempotent, but it is not necessarily so. For instance, a PATCH request may increment a specific field every time it is called, which would modify the resource every time.

What is Service Lifetime in Dependency injection and why do we use each of them?
How is dependecny injected in controllers?


What is the differenc between throw and throw ex?


Docker :

What is the difference between image & container?

What is container orchestration and why should we use it?

How does Kubernetes handle container scaling?


Agile

How do you measure the velocity of your team?

How do you conduct sprint outcomes?(reviews/restrospectives)

What methodology do you follow for estimation of work items?What factors should you consider?


Analytical/Behavioural

What was the most challenging problem/issue that you solved and how was it solved?


Cloud

Can you describe your experience with cloud systems, particularly how you have utilized Azure in previous projects

CI/CD

How can you enforce branch policies ?
Explain a typical structure of an Azure DevOps YAML Pipeline.
How would you use IaaC tools such as ARM Template or Terraform to automate Infra provisioning?
What is the difference between Microsoft-hosted agents and self-hosted agents?
 How can you ensure the security and privacy of secrets used in your pipeline to prevent their exposure?
How would you implement CICD for a container-based application?

