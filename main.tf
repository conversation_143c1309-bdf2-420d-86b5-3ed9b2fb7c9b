resource "aws_s3_bucket" "datacenter-stream-bucket" {
  bucket = var.KKE_S3_BUCKET_NAME
}

resource "aws_iam_role" "firehose-role" {
  name        = var.KKE_FIREHOSE_ROLE_NAME
  description = "Role for <PERSON><PERSON><PERSON> Firehose to put objects into S3"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Principal = {
          Service = "firehose.amazonaws.com"
        }
        Effect = "Allow"
      }
    ]
  })
}

resource "aws_iam_policy" "firehose_s3_policy" {
  name = "firehose_s3_policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl"
        ]
        Resource = "${aws_s3_bucket.datacenter-stream-bucket.arn}/*"
    }]
  })
}

resource "aws_iam_role_policy_attachment" "firehose_s3_policy_attachment" {
  role       = aws_iam_role.firehose-role.name
  policy_arn = aws_iam_policy.firehose_s3_policy.arn
}

resource "aws_kinesis_firehose_delivery_stream" "firehose" {
  name        = var.KKE_FIREHOSE_STREAM_NAME
  destination = "extended_s3"
  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose-role.arn
    bucket_arn         = aws_s3_bucket.datacenter-stream-bucket.arn
    buffering_size     = 5
    buffering_interval = 300
    processing_configuration {
      processors {
        type = "AppendDelimiterToRecord"
        parameters {
          parameter_name  = "Delimiter"
          parameter_value = "\n"
        }
      }
    }
  }
  depends_on = [aws_iam_role.firehose-role]
}

variable "KKE_S3_BUCKET_NAME" {
  type    = string
  default = "datacenter-stream-bucket-2025"
}
variable "KKE_FIREHOSE_STREAM_NAME" {
  type    = string
  default = "datacenter-firehose-stream"
}

variable "KKE_FIREHOSE_ROLE_NAME" {
  type    = string
  default = "firehose-sts-role"
}

output "kke_firehose_stream_name"{
  value = aws_kinesis_firehose_delivery_stream.firehose.name
}

output "kke_s3_bucket_name"{
  value = aws_s3_bucket.datacenter-stream-bucket.bucket
}

output "kke_firehose_role_arn"{
  value = aws_iam_role.firehose-role.arn
}